import Header from "@/components/layout/Header";
import { SubscriptionCard } from "@/components/subscription/SubscriptionCard";
import { useAuth } from "@/hooks/useAuth";
import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { useSubscription } from "@/hooks/useSubscription";

export default function Subscription() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const { checkSubscription } = useSubscription();

  // Handle success/cancel from Stripe
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const success = params.get('success');
    const canceled = params.get('canceled');

    if (success) {
      toast({
        title: "¡Suscripción Exitosa!",
        description: "Tu suscripción premium está ahora activa.",
      });
      // Refresh subscription status
      checkSubscription();
      // Clean up URL
      navigate('/suscripcion', { replace: true });
    } else if (canceled) {
      toast({
        title: "Suscripción Cancelada",
        description: "No se procesó el pago. Puedes intentar nuevamente cuando gustes.",
        variant: "destructive",
      });
      // Clean up URL
      navigate('/suscripcion', { replace: true });
    }
  }, [location.search, toast, checkSubscription, navigate]);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold mb-4">Suscripción Premium</h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Accede a todas las funcionalidades premium de nuestra plataforma de vivienda accesible
            </p>
          </div>

          <div className="flex justify-center">
            <SubscriptionCard />
          </div>

          <div className="mt-12 text-center text-sm text-muted-foreground">
            <p>Cancelación en cualquier momento • Sin compromisos a largo plazo</p>
            <p className="mt-2">
              ¿Necesitas ayuda? <a href="mailto:<EMAIL>" className="text-primary hover:underline">Contáctanos</a>
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}