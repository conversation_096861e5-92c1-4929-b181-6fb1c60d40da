import React, { useState } from 'react';
import Header from '@/components/layout/Header';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { usePropertyManagement, type PropertyInput } from '@/hooks/usePropertyManagement';
import { Link } from 'react-router-dom';
import { 
  Plus, 
  Home, 
  Eye, 
  Edit, 
  Trash2, 
  Users, 
  MessageCircle, 
  TrendingUp,
  Calendar,
  Star,
  Upload,
  CheckCircle,
  Loader2,
  Pause,
  Play
} from 'lucide-react';

const Dashboard = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isCreating, setIsCreating] = useState(false);
  
  const { user } = useAuth();
  const { properties, stats, loading, error, createProperty, deleteProperty, togglePropertyStatus } = usePropertyManagement();
  const { toast } = useToast();

  const [newProperty, setNewProperty] = useState<PropertyInput>({
    title: '',
    description: '',
    price: 0,
    location: '',
    city: '',
    province: '',
    property_type: 'apartment',
    accessibility_level: 'basic',
    bedrooms: 1,
    bathrooms: 1,
    size_sqm: 0
  });

  const handleCreateProperty = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsCreating(true);
      await createProperty(newProperty);
      
      toast({
        title: "Propiedad creada",
        description: "Tu propiedad ha sido publicada exitosamente.",
      });
      
      // Reset form
      setNewProperty({
        title: '',
        description: '',
        price: 0,
        location: '',
        city: '',
        province: '',
        property_type: 'apartment',
        accessibility_level: 'basic',
        bedrooms: 1,
        bathrooms: 1,
        size_sqm: 0
      });
      
      // Switch to properties tab
      setActiveTab('properties');
      
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "No se pudo crear la propiedad.",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteProperty = async (propertyId: string) => {
    if (!window.confirm('¿Estás seguro de que quieres eliminar esta propiedad?')) {
      return;
    }
    
    try {
      await deleteProperty(propertyId);
      toast({
        title: "Propiedad eliminada",
        description: "La propiedad ha sido eliminada exitosamente.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo eliminar la propiedad.",
        variant: "destructive",
      });
    }
  };

  const handleToggleStatus = async (propertyId: string) => {
    try {
      await togglePropertyStatus(propertyId);
      toast({
        title: "Estado actualizado",
        description: "El estado de la propiedad ha sido actualizado.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo actualizar el estado.",
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge className="bg-green-100 text-green-800">Activa</Badge>
    ) : (
      <Badge className="bg-gray-100 text-gray-800">Inactiva</Badge>
    );
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Acceso Restringido</h1>
            <p className="text-muted-foreground mb-6">
              Debes iniciar sesión para acceder al panel de propietario.
            </p>
            <Button asChild>
              <Link to="/auth">Iniciar Sesión</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Panel de Propietario</h1>
          <p className="text-muted-foreground">
            Gestiona tus propiedades y consultas desde un solo lugar
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Resumen</TabsTrigger>
            <TabsTrigger value="properties">Propiedades</TabsTrigger>
            <TabsTrigger value="inquiries">Consultas</TabsTrigger>
            <TabsTrigger value="add-property">Añadir Propiedad</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Stats Cards */}
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Card key={i}>
                    <CardContent className="p-6">
                      <Skeleton className="h-16" />
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Total Propiedades</p>
                        <p className="text-2xl font-bold">{stats.totalProperties}</p>
                      </div>
                      <Home className="w-8 h-8 text-primary" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Activas</p>
                        <p className="text-2xl font-bold text-green-600">{stats.activeListings}</p>
                      </div>
                      <CheckCircle className="w-8 h-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Visualizaciones</p>
                        <p className="text-2xl font-bold">{stats.totalViews}</p>
                      </div>
                      <Eye className="w-8 h-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Consultas</p>
                        <p className="text-2xl font-bold">{stats.inquiries}</p>
                      </div>
                      <MessageCircle className="w-8 h-8 text-orange-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Valoración</p>
                        <p className="text-2xl font-bold">{stats.averageRating}</p>
                      </div>
                      <Star className="w-8 h-8 text-yellow-500 fill-current" />
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Actividad Reciente</CardTitle>
                <CardDescription>
                  Últimas interacciones con tus propiedades
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <div className="flex-1">
                      <p className="text-sm">Nueva consulta en "Apartamento moderno centro Madrid"</p>
                      <p className="text-xs text-muted-foreground">Hace 2 horas</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    <div className="flex-1">
                      <p className="text-sm">Tu propiedad ha sido vista 15 veces hoy</p>
                      <p className="text-xs text-muted-foreground">Hace 4 horas</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-orange-500 rounded-full" />
                    <div className="flex-1">
                      <p className="text-sm">Nueva valoración: 5 estrellas</p>
                      <p className="text-xs text-muted-foreground">Ayer</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Properties Tab */}
          <TabsContent value="properties" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Mis Propiedades</h2>
              <Button onClick={() => setActiveTab('add-property')}>
                <Plus size={16} className="mr-2" />
                Añadir Propiedad
              </Button>
            </div>

            {loading ? (
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <Card key={i}>
                    <CardContent className="p-6">
                      <Skeleton className="h-24" />
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : error ? (
              <Card className="border-destructive">
                <CardContent className="pt-6">
                  <p className="text-destructive text-center">{error}</p>
                </CardContent>
              </Card>
            ) : properties.length === 0 ? (
              <Card>
                <CardContent className="pt-6 text-center">
                  <Home className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">No tienes propiedades aún</h3>
                  <p className="text-muted-foreground mb-4">
                    Comienza añadiendo tu primera propiedad accesible.
                  </p>
                  <Button onClick={() => setActiveTab('add-property')}>
                    <Plus size={16} className="mr-2" />
                    Añadir Primera Propiedad
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid gap-6">
                {properties.map((property) => (
                  <Card key={property.id}>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-lg font-semibold">{property.title}</h3>
                            {getStatusBadge(property.is_active)}
                          </div>
                          <div className="flex items-center gap-6 text-sm text-muted-foreground">
                            <span>€{property.price.toLocaleString()}/mes</span>
                            <span className="flex items-center gap-1">
                              <Eye size={14} />
                              {Math.floor(Math.random() * 100) + 20} vistas
                            </span>
                            <span className="flex items-center gap-1">
                              <MessageCircle size={14} />
                              {Math.floor(Math.random() * 10) + 1} consultas
                            </span>
                            <span>Actualizado: {new Date(property.updated_at).toLocaleDateString()}</span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm" asChild>
                            <Link to={`/propiedad/${property.id}`}>
                              <Eye size={14} className="mr-1" />
                              Ver
                            </Link>
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleToggleStatus(property.id)}
                          >
                            {property.is_active ? (
                              <>
                                <Pause size={14} className="mr-1" />
                                Pausar
                              </>
                            ) : (
                              <>
                                <Play size={14} className="mr-1" />
                                Activar
                              </>
                            )}
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="text-destructive"
                            onClick={() => handleDeleteProperty(property.id)}
                          >
                            <Trash2 size={14} />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          {/* Inquiries Tab */}
          <TabsContent value="inquiries" className="space-y-6">
            <h2 className="text-2xl font-bold">Consultas Recibidas</h2>

            <Card>
              <CardContent className="pt-6 text-center">
                <MessageCircle className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">Sistema de consultas próximamente</h3>
                <p className="text-muted-foreground">
                  Esta funcionalidad estará disponible próximamente. Los usuarios podrán contactarte 
                  directamente desde cada propiedad usando WhatsApp y formularios de contacto.
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Add Property Tab */}
          <TabsContent value="add-property" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Añadir Nueva Propiedad</CardTitle>
                <CardDescription>
                  Completa la información básica de tu propiedad accesible
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleCreateProperty} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="title">Título de la propiedad *</Label>
                      <Input
                        id="title"
                        placeholder="Ej: Apartamento accesible centro Madrid"
                        value={newProperty.title}
                        onChange={(e) => setNewProperty(prev => ({ ...prev, title: e.target.value }))}
                        required
                        maxLength={200}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="price">Precio mensual (€) *</Label>
                      <Input
                        id="price"
                        type="number"
                        min="0"
                        placeholder="850"
                        value={newProperty.price || ''}
                        onChange={(e) => setNewProperty(prev => ({ ...prev, price: parseInt(e.target.value) || 0 }))}
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="location">Ubicación *</Label>
                      <Input
                        id="location"
                        placeholder="Dirección completa"
                        value={newProperty.location}
                        onChange={(e) => setNewProperty(prev => ({ ...prev, location: e.target.value }))}
                        required
                        maxLength={255}
                      />
                    </div>
                    <div>
                      <Label htmlFor="city">Ciudad</Label>
                      <Input
                        id="city"
                        placeholder="Madrid"
                        value={newProperty.city || ''}
                        onChange={(e) => setNewProperty(prev => ({ ...prev, city: e.target.value }))}
                        maxLength={100}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="property-type">Tipo de propiedad *</Label>
                      <Select 
                        value={newProperty.property_type} 
                        onValueChange={(value: any) => setNewProperty(prev => ({ ...prev, property_type: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Seleccionar" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="apartment">Apartamento</SelectItem>
                          <SelectItem value="house">Casa</SelectItem>
                          <SelectItem value="assisted-living">Residencia Asistida</SelectItem>
                          <SelectItem value="senior-community">Comunidad para Seniors</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="accessibility-level">Nivel de accesibilidad *</Label>
                      <Select 
                        value={newProperty.accessibility_level} 
                        onValueChange={(value: any) => setNewProperty(prev => ({ ...prev, accessibility_level: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Seleccionar nivel" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="basic">Básica</SelectItem>
                          <SelectItem value="intermediate">Intermedia</SelectItem>
                          <SelectItem value="advanced">Avanzada</SelectItem>
                          <SelectItem value="full">Completamente Adaptada</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <Label htmlFor="bedrooms">Habitaciones *</Label>
                      <Input
                        id="bedrooms"
                        type="number"
                        min="0"
                        placeholder="2"
                        value={newProperty.bedrooms || ''}
                        onChange={(e) => setNewProperty(prev => ({ ...prev, bedrooms: parseInt(e.target.value) || 0 }))}
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="bathrooms">Baños *</Label>
                      <Input
                        id="bathrooms"
                        type="number"
                        min="0"
                        placeholder="1"
                        value={newProperty.bathrooms || ''}
                        onChange={(e) => setNewProperty(prev => ({ ...prev, bathrooms: parseInt(e.target.value) || 0 }))}
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="area">Superficie (m²)</Label>
                      <Input
                        id="area"
                        type="number"
                        min="0"
                        placeholder="85"
                        value={newProperty.size_sqm || ''}
                        onChange={(e) => setNewProperty(prev => ({ ...prev, size_sqm: parseInt(e.target.value) || 0 }))}
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="description">Descripción *</Label>
                    <Textarea
                      id="description"
                      placeholder="Describe las características de accesibilidad y comodidades de tu propiedad..."
                      rows={4}
                      value={newProperty.description}
                      onChange={(e) => setNewProperty(prev => ({ ...prev, description: e.target.value }))}
                      required
                      maxLength={2000}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      {newProperty.description.length}/2000 caracteres
                    </p>
                  </div>

                  <div className="border-2 border-dashed border-muted rounded-lg p-8 text-center">
                    <Upload className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                    <h3 className="font-semibold mb-2">Subir fotos</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Sube hasta 10 fotos de tu propiedad. Incluye imágenes de las adaptaciones de accesibilidad.
                    </p>
                    <Button type="button" variant="outline">
                      Seleccionar fotos
                    </Button>
                  </div>

                  <div className="flex justify-end gap-4">
                    <Button type="button" variant="outline">
                      Guardar como borrador
                    </Button>
                    <Button type="submit" className="bg-primary hover:bg-primary-dark">
                      Publicar propiedad
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Dashboard;