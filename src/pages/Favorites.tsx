import React, { useState, useEffect } from 'react';
import Header from '@/components/layout/Header';
import PropertyCardNew from '@/components/properties/PropertyCardNew';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Heart, Filter, Grid, List, Trash2, Loader2 } from 'lucide-react';
import { useFavorites } from '@/hooks/useFavorites';
import { useToast } from '@/hooks/use-toast';
import type { Property } from '@/hooks/useProperties';
import { Link } from 'react-router-dom';

const Favorites = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('recent');
  const [favoriteProperties, setFavoriteProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  
  const { favorites, toggleFavorite, fetchFavoriteProperties } = useFavorites();
  const { toast } = useToast();

  useEffect(() => {
    const loadFavorites = async () => {
      try {
        setLoading(true);
        const properties = await fetchFavoriteProperties();
        setFavoriteProperties(properties as Property[]);
      } catch (error) {
        toast({
          title: "Error",
          description: "No se pudieron cargar las propiedades favoritas.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadFavorites();
  }, [fetchFavoriteProperties, toast]);

  const handleToggleFavorite = async (propertyId: string) => {
    try {
      await toggleFavorite(propertyId);
      setFavoriteProperties(prev => 
        prev.filter(property => property.id !== propertyId)
      );
      toast({
        title: "Eliminado de favoritos",
        description: "La propiedad se ha eliminado de tus favoritos.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo eliminar de favoritos.",
        variant: "destructive",
      });
    }
  };

  const handleClearAllFavorites = async () => {
    if (window.confirm('¿Estás seguro de que quieres eliminar todas las propiedades de favoritos?')) {
      try {
        // Remove all favorites
        for (const property of favoriteProperties) {
          await toggleFavorite(property.id);
        }
        setFavoriteProperties([]);
        toast({
          title: "Favoritos eliminados",
          description: "Todas las propiedades han sido eliminadas de favoritos.",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "No se pudieron eliminar todos los favoritos.",
          variant: "destructive",
        });
      }
    }
  };

  const handleSortChange = (value: string) => {
    setSortBy(value);
    let sorted = [...favoriteProperties];
    
    switch (value) {
      case 'price-low':
        sorted.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        sorted.sort((a, b) => b.price - a.price);
        break;
      case 'alphabetical':
        sorted.sort((a, b) => a.title.localeCompare(b.title));
        break;
      default:
        // Más recientes - mantener orden original
        break;
    }
    
    setFavoriteProperties(sorted);
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 bg-red-100 rounded-lg">
              <Heart className="w-6 h-6 text-red-500 fill-current" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">Mis Favoritos</h1>
              <p className="text-muted-foreground">
                {favoriteProperties.length} propiedades guardadas
              </p>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="text-center py-16">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Cargando favoritos...</h2>
            <p className="text-muted-foreground">Obteniendo tus propiedades guardadas</p>
          </div>
        ) : favoriteProperties.length === 0 ? (
          /* Empty State */
          <div className="text-center py-16">
            <div className="w-24 h-24 mx-auto mb-6 bg-muted rounded-full flex items-center justify-center">
              <Heart size={32} className="text-muted-foreground" />
            </div>
            <h2 className="text-2xl font-semibold mb-4">No tienes favoritos aún</h2>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              Cuando encuentres propiedades que te gusten, puedes guardarlas aquí 
              haciendo clic en el corazón.
            </p>
            <Button size="lg" asChild>
              <Link to="/propiedades">
                Explorar Propiedades
              </Link>
            </Button>
          </div>
        ) : (
          <>
            {/* Controls */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
              <div className="flex items-center gap-4">
                <h2 className="text-xl font-semibold">
                  {favoriteProperties.length} {favoriteProperties.length === 1 ? 'propiedad guardada' : 'propiedades guardadas'}
                </h2>
              </div>
              
              <div className="flex items-center gap-4">
                {/* Clear All */}
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleClearAllFavorites}
                  className="text-destructive hover:text-destructive"
                >
                  <Trash2 size={16} className="mr-2" />
                  Limpiar Todo
                </Button>

                {/* Sort */}
                <div className="flex items-center gap-2">
                  <Filter size={16} className="text-muted-foreground" />
                  <Select value={sortBy} onValueChange={handleSortChange}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Ordenar por" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="recent">Más Recientes</SelectItem>
                      <SelectItem value="price-low">Precio: Menor a Mayor</SelectItem>
                      <SelectItem value="price-high">Precio: Mayor a Menor</SelectItem>
                      <SelectItem value="alphabetical">Alfabético</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* View Mode */}
                <div className="flex rounded-lg border">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-r-none"
                    aria-label="Vista en cuadrícula"
                  >
                    <Grid size={16} />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-l-none"
                    aria-label="Vista en lista"
                  >
                    <List size={16} />
                  </Button>
                </div>
              </div>
            </div>

            {/* Properties Grid/List */}
            <div className={
              viewMode === 'grid' 
                ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                : 'space-y-4'
            }>
            {favoriteProperties.map((property) => (
              <PropertyCardNew
                key={property.id}
                property={property}
                className={viewMode === 'list' ? 'flex flex-row' : ''}
              />
            ))}
            </div>

            {/* Tips */}
            <div className="mt-12 p-6 bg-muted/50 rounded-lg">
              <h3 className="font-semibold text-lg mb-3">💡 Consejos para gestionar tus favoritos</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-muted-foreground">
                <div>
                  <strong>Organiza tu búsqueda:</strong> Guarda propiedades que cumplan diferentes criterios 
                  para poder compararlas fácilmente.
                </div>
                <div>
                  <strong>Actúa rápido:</strong> Las propiedades accesibles son muy demandadas. 
                  Contacta a los propietarios lo antes posible.
                </div>
                <div>
                  <strong>Filtra por ubicación:</strong> Considera la proximidad a servicios médicos 
                  y transporte público accesible.
                </div>
                <div>
                  <strong>Comparte con familia:</strong> Puedes compartir tus favoritos con familiares 
                  para que te ayuden en la decisión.
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default Favorites;