import React, { useState } from 'react';
import Header from '@/components/layout/Header';
import SearchBar from '@/components/search/SearchBar';
import PropertyCardNew from '@/components/properties/PropertyCardNew';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Filter, Grid, List, Loader2 } from 'lucide-react';
import { useProperties } from '@/hooks/useProperties';
import { Card, CardContent } from '@/components/ui/card';

const Properties = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('relevance');
  const { properties, loading, error, fetchProperties } = useProperties();

  const handleSearch = (filters: {
    location?: string;
    propertyType?: string;
    accessibilityLevel?: string;
    minPrice?: number;
    maxPrice?: number;
  }) => {
    console.log('Filtros aplicados:', filters);
    fetchProperties(filters);
  };

  const handleSortChange = (value: string) => {
    setSortBy(value);
    // TODO: Implement sorting with Supabase
    console.log('Sort by:', value);
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Search Section */}
      <section className="py-8 px-4 bg-muted/50">
        <div className="container mx-auto">
          <div className="text-center mb-6">
            <h1 className="text-3xl font-bold mb-2">Encuentra tu hogar ideal</h1>
            <p className="text-muted-foreground">
              {loading ? 'Buscando propiedades...' : `${properties.length} propiedades adaptadas disponibles`}
            </p>
          </div>
          
          <SearchBar onSearch={handleSearch} className="max-w-6xl mx-auto" />
        </div>
      </section>

      {/* Results Section */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          {/* Controls */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <div className="flex items-center gap-4">
              <h2 className="text-xl font-semibold">
                {loading ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="w-5 h-5 animate-spin" />
                    Buscando propiedades...
                  </div>
                ) : (
                  `${properties.length} propiedades encontradas`
                )}
              </h2>
            </div>
            
            <div className="flex items-center gap-4">
              {/* Sort */}
              <div className="flex items-center gap-2">
                <Filter size={16} className="text-muted-foreground" />
                <Select value={sortBy} onValueChange={handleSortChange}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Ordenar por" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="relevance">Relevancia</SelectItem>
                    <SelectItem value="price-low">Precio: Menor a Mayor</SelectItem>
                    <SelectItem value="price-high">Precio: Mayor a Menor</SelectItem>
                    <SelectItem value="newest">Más Recientes</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* View Mode */}
              <div className="flex rounded-lg border">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="rounded-r-none"
                  aria-label="Vista en cuadrícula"
                >
                  <Grid size={16} />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="rounded-l-none"
                  aria-label="Vista en lista"
                >
                  <List size={16} />
                </Button>
              </div>
            </div>
          </div>

          {/* Error State */}
          {error && (
            <Card className="mb-6 border-destructive">
              <CardContent className="pt-6">
                <p className="text-destructive text-center">{error}</p>
                <div className="text-center mt-4">
                  <Button 
                    onClick={() => fetchProperties()} 
                    variant="outline"
                  >
                    Intentar de nuevo
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Loading State */}
          {loading && (
            <div className={
              viewMode === 'grid' 
                ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                : 'space-y-4'
            }>
              {Array.from({ length: 6 }).map((_, index) => (
                <Card key={index} className="animate-pulse">
                  <div className="h-48 bg-muted rounded-t-lg" />
                  <CardContent className="p-4">
                    <div className="h-4 bg-muted rounded mb-2" />
                    <div className="h-6 bg-muted rounded mb-2" />
                    <div className="h-4 bg-muted rounded w-2/3" />
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Properties Grid/List */}
          {!loading && !error && (
            <>
              {properties.length === 0 ? (
                <div className="text-center py-12">
                  <div className="w-24 h-24 mx-auto mb-4 bg-muted rounded-full flex items-center justify-center">
                    <Filter size={32} className="text-muted-foreground" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">No se encontraron propiedades</h3>
                  <p className="text-muted-foreground mb-4">
                    Intenta ajustar tus filtros de búsqueda para encontrar más opciones.
                  </p>
                  <Button onClick={() => fetchProperties()}>
                    Limpiar Filtros
                  </Button>
                </div>
              ) : (
                <div className={
                  viewMode === 'grid' 
                    ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                    : 'space-y-4'
                }>
                  {properties.map((property) => (
                    <PropertyCardNew
                      key={property.id}
                      property={property}
                      className={viewMode === 'list' ? 'flex flex-row' : ''}
                    />
                  ))}
                </div>
              )}
            </>
          )}

          {/* Load More Button */}
          {!loading && properties.length > 0 && (
            <div className="text-center mt-8">
              <Button variant="outline" size="lg" disabled>
                Cargar más propiedades (próximamente)
              </Button>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 px-4 bg-primary text-white">
        <div className="container mx-auto text-center">
          <h2 className="text-2xl font-bold mb-4">¿No encuentras lo que buscas?</h2>
          <p className="text-lg mb-6 opacity-90">
            Nuestro equipo puede ayudarte a encontrar la propiedad perfecta para tus necesidades específicas.
          </p>
          <Button 
            size="lg" 
            className="bg-white text-primary hover:bg-gray-100"
          >
            Contactar con un Experto
          </Button>
        </div>
      </section>
    </div>
  );
};

export default Properties;