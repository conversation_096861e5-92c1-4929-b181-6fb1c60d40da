import React, { useState } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import Header from '@/components/layout/Header';
import AccessibilityBadges from '@/components/properties/AccessibilityBadges';
import ContactModal from '@/components/contact/ContactModal';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { useProperty } from '@/hooks/useProperties';
import { useFavorites } from '@/hooks/useFavorites';
import { useToast } from '@/hooks/use-toast';
import { 
  ArrowLeft, 
  Heart, 
  Share2, 
  MapPin, 
  Users, 
  Bath, 
  Ruler,
  Calendar,
  Phone,
  Mail,
  MessageCircle,
  Star,
  Shield,
  Loader2
} from 'lucide-react';

const PropertyDetail = () => {
  const { id } = useParams();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showContactModal, setShowContactModal] = useState(false);
  const { property, loading, error } = useProperty(id || '');
  const { favorites, toggleFavorite } = useFavorites();
  const { toast } = useToast();

  const isFavorite = property ? favorites.includes(property.id) : false;

  const getAccessibilityLevel = (level: string) => {
    const levels = {
      basic: { label: 'Accesibilidad Básica', color: 'bg-yellow-100 text-yellow-800' },
      intermediate: { label: 'Accesibilidad Intermedia', color: 'bg-blue-100 text-blue-800' },
      advanced: { label: 'Accesibilidad Avanzada', color: 'bg-green-100 text-green-800' },
      full: { label: 'Totalmente Adaptada', color: 'bg-accent-green/10 text-accent-green' }
    };
    return levels[level as keyof typeof levels] || levels.basic;
  };

  const handleContactOwner = () => {
    setShowContactModal(true);
  };

  const handleToggleFavorite = async () => {
    if (!property) return;
    
    try {
      await toggleFavorite(property.id);
      toast({
        title: isFavorite ? "Eliminado de favoritos" : "Agregado a favoritos",
        description: isFavorite 
          ? "La propiedad se ha eliminado de tus favoritos." 
          : "La propiedad se ha guardado en tus favoritos.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo actualizar los favoritos. Inténtalo de nuevo.",
        variant: "destructive",
      });
    }
  };

  const handleShare = async () => {
    if (!property) return;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: property.title,
          text: `Mira esta propiedad accesible: ${property.title}`,
          url: window.location.href,
        });
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      try {
        await navigator.clipboard.writeText(window.location.href);
        toast({
          title: "Enlace copiado",
          description: "El enlace se ha copiado al portapapeles.",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "No se pudo copiar el enlace.",
          variant: "destructive",
        });
      }
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="container mx-auto px-4 py-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-6">
              <Skeleton className="h-96 w-full" />
              <Card>
                <CardContent className="p-6 space-y-4">
                  <Skeleton className="h-8 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                  <div className="grid grid-cols-4 gap-4">
                    {Array.from({ length: 4 }).map((_, i) => (
                      <Skeleton key={i} className="h-16" />
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
            <div className="space-y-6">
              <Skeleton className="h-64" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !property) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="container mx-auto px-4 py-6">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold mb-4">Propiedad no encontrada</h1>
            <p className="text-muted-foreground mb-6">
              {error || "La propiedad que buscas no existe o no está disponible."}
            </p>
            <Button asChild>
              <Link to="/propiedades">Volver a Propiedades</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const accessibilityLevel = getAccessibilityLevel(property.accessibility_level);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container mx-auto px-4 py-6">
        {/* Back Navigation */}
        <Button variant="ghost" asChild className="mb-4">
          <Link to="/propiedades" className="flex items-center gap-2">
            <ArrowLeft size={16} />
            Volver a propiedades
          </Link>
        </Button>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Image Gallery */}
            <Card>
              <CardContent className="p-0">
                <div className="relative">
                  <img
                    src={property.images?.[currentImageIndex] || '/placeholder.svg'}
                    alt={`Vista ${currentImageIndex + 1} de ${property.title}`}
                    className="w-full h-96 object-cover rounded-t-lg"
                  />
                  
                  {/* Image Navigation */}
                  {property.images && property.images.length > 1 && (
                    <div className="absolute bottom-4 left-1/2 -translate-x-1/2">
                      <div className="flex space-x-2">
                        {property.images.map((_, index) => (
                          <button
                            key={index}
                            onClick={() => setCurrentImageIndex(index)}
                            className={`w-3 h-3 rounded-full transition-colors ${
                              index === currentImageIndex 
                                ? 'bg-white' 
                                : 'bg-white/50 hover:bg-white/75'
                            }`}
                            aria-label={`Ver imagen ${index + 1}`}
                          />
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="absolute top-4 right-4 flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleToggleFavorite}
                      className="bg-white/90 backdrop-blur-sm"
                      aria-label={isFavorite ? 'Quitar de favoritos' : 'Agregar a favoritos'}
                    >
                      <Heart size={16} className={isFavorite ? 'fill-red-500 text-red-500' : ''} />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleShare}
                      className="bg-white/90 backdrop-blur-sm"
                      aria-label="Compartir propiedad"
                    >
                      <Share2 size={16} />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Property Info */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <Badge variant="secondary" className="mb-2">
                      {property.property_type === 'apartment' ? 'Apartamento' : 
                       property.property_type === 'house' ? 'Casa' :
                       property.property_type === 'assisted-living' ? 'Residencia Asistida' : 'Comunidad'}
                    </Badge>
                    <CardTitle className="text-2xl mb-2">{property.title}</CardTitle>
                    <div className="flex items-center text-muted-foreground">
                      <MapPin size={16} className="mr-1" />
                      {property.location}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold text-primary">
                      €{property.price.toLocaleString()}
                      <span className="text-lg font-normal text-muted-foreground">/mes</span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Fianza: €{(property.price * 2).toLocaleString()}
                    </div>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-6">
                {/* Quick Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-muted rounded-lg">
                    <Users className="w-6 h-6 mx-auto mb-1 text-primary" />
                    <div className="font-semibold">{property.bedrooms}</div>
                    <div className="text-xs text-muted-foreground">Habitaciones</div>
                  </div>
                  <div className="text-center p-3 bg-muted rounded-lg">
                    <Bath className="w-6 h-6 mx-auto mb-1 text-primary" />
                    <div className="font-semibold">{property.bathrooms}</div>
                    <div className="text-xs text-muted-foreground">Baños</div>
                  </div>
                  <div className="text-center p-3 bg-muted rounded-lg">
                    <Ruler className="w-6 h-6 mx-auto mb-1 text-primary" />
                    <div className="font-semibold">{property.size_sqm || 85}m²</div>
                    <div className="text-xs text-muted-foreground">Superficie</div>
                  </div>
                  <div className="text-center p-3 bg-muted rounded-lg">
                    <Calendar className="w-6 h-6 mx-auto mb-1 text-primary" />
                    <div className="font-semibold">{new Date(property.created_at).getFullYear()}</div>
                    <div className="text-xs text-muted-foreground">Publicado</div>
                  </div>
                </div>

                {/* Accessibility Level */}
                <div>
                  <Badge className={`${accessibilityLevel.color} border text-sm px-3 py-1`}>
                    {accessibilityLevel.label}
                  </Badge>
                </div>

                {/* Description */}
                <div>
                  <h3 className="font-semibold text-lg mb-3">Descripción</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {property.description}
                  </p>
                </div>

                <Separator />

                {/* Accessibility Features */}
                <div>
                  <h3 className="font-semibold text-lg mb-3">Características de Accesibilidad</h3>
                  {property.accessibility_features && property.accessibility_features.length > 0 ? (
                    <AccessibilityBadges features={property.accessibility_features} showAll />
                  ) : (
                    <p className="text-muted-foreground">No hay características de accesibilidad específicas listadas.</p>
                  )}
                </div>

                <Separator />

                {/* Neighborhood */}
                <div>
                  <h3 className="font-semibold text-lg mb-3">Ubicación</h3>
                  <p className="text-muted-foreground mb-4">
                    Zona con excelente conectividad de transporte público y servicios cercanos adaptados para personas con diferentes necesidades de accesibilidad.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <div className="flex items-center text-sm">
                      <div className="w-2 h-2 bg-primary rounded-full mr-2" />
                      Transporte público accesible
                    </div>
                    <div className="flex items-center text-sm">
                      <div className="w-2 h-2 bg-primary rounded-full mr-2" />
                      Centros médicos cercanos
                    </div>
                    <div className="flex items-center text-sm">
                      <div className="w-2 h-2 bg-primary rounded-full mr-2" />
                      Farmacias en la zona
                    </div>
                    <div className="flex items-center text-sm">
                      <div className="w-2 h-2 bg-primary rounded-full mr-2" />
                      Servicios adaptados
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Contact Card */}
            <Card className="sticky top-6">
              <CardHeader>
                <CardTitle className="text-xl">Contactar Propietario</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Owner Info */}
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-white font-semibold">
                    P
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-semibold">Propietario Verificado</span>
                      <Shield size={16} className="text-accent-green" aria-label="Propietario verificado" />
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Star size={12} className="mr-1 fill-yellow-400 text-yellow-400" />
                      4.8 • Responde rápido
                    </div>
                  </div>
                </div>

                <div className="text-sm text-muted-foreground">
                  Especializado en propiedades accesibles
                </div>

                <Separator />

                {/* Contact Buttons */}
                <div className="space-y-3">
                  <Button 
                    className="w-full bg-primary hover:bg-primary-dark"
                    onClick={handleContactOwner}
                  >
                    <MessageCircle size={16} className="mr-2" />
                    Enviar Mensaje
                  </Button>
                  
                  <Button variant="outline" className="w-full">
                    <Phone size={16} className="mr-2" />
                    Llamar Ahora
                  </Button>
                  
                  <Button variant="outline" className="w-full">
                    <Mail size={16} className="mr-2" />
                    Enviar Email
                  </Button>
                </div>

                <div className="text-xs text-muted-foreground text-center">
                  Al contactar, menciona que vienes de CasaAccesible
                </div>
              </CardContent>
            </Card>

            {/* Availability */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Disponibilidad</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Disponible desde:</span>
                  <span className="font-semibold">Inmediatamente</span>
                </div>
              </CardContent>
            </Card>

            {/* Safety Notice */}
            <Card className="bg-accent-green/5 border-accent-green/20">
              <CardContent className="pt-6">
                <div className="flex items-start space-x-3">
                  <Shield className="w-6 h-6 text-accent-green mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-accent-green mb-2">Propiedad Verificada</h4>
                    <p className="text-sm text-muted-foreground">
                      Esta propiedad ha sido inspeccionada por nuestro equipo especializado 
                      en accesibilidad y cumple con todos los estándares certificados.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Contact Modal */}
        {property && (
          <ContactModal
            open={showContactModal}
            onOpenChange={setShowContactModal}
            propertyTitle={property.title}
            propertyId={property.id}
          />
        )}
      </div>
    </div>
  );
};

export default PropertyDetail;