import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import SearchBar from '@/components/search/SearchBar';
import PropertyCard from '@/components/properties/PropertyCard';
import Header from '@/components/layout/Header';
import { 
  Heart, 
  Shield, 
  Users, 
  Search, 
  Accessibility, 
  Star,
  ArrowRight,
  CheckCircle,
  Crown
} from 'lucide-react';
import { Link } from 'react-router-dom';
import heroImage from '@/assets/hero-accessible-housing.jpg';
import type { Property } from '@/components/properties/PropertyCard';
import { defaultAccessibilityFeatures } from '@/components/properties/AccessibilityBadges';

const Home = () => {
  // Mock data for featured properties
  const featuredProperties: Property[] = [
    {
      id: '1',
      title: 'Apartamento moderno con todas las adaptaciones',
      description: 'Espacioso apartamento de 2 habitaciones completamente adaptado para personas con movilidad reducida.',
      price: 850,
      location: 'Madrid Centro',
      images: ['/placeholder.svg'],
      propertyType: 'apartment',
      accessibility: {
        level: 'full',
        features: defaultAccessibilityFeatures
      },
      bedrooms: 2,
      bathrooms: 2,
      isFavorite: false
    },
    {
      id: '2',
      title: 'Casa unifamiliar con jardín accesible',
      description: 'Casa de una planta con jardín adaptado y todas las comodidades para una vida independiente.',
      price: 1200,
      location: 'Barcelona, Eixample',
      images: ['/placeholder.svg'],
      propertyType: 'house',
      accessibility: {
        level: 'advanced',
        features: defaultAccessibilityFeatures.slice(0, 5)
      },
      bedrooms: 3,
      bathrooms: 2,
      isFavorite: true
    },
    {
      id: '3',
      title: 'Residencia asistida con servicios completos',
      description: 'Apartamento en residencia con servicios de asistencia 24/7 y actividades sociales.',
      price: 1800,
      location: 'Valencia, Centro',
      images: ['/placeholder.svg'],
      propertyType: 'assisted-living',
      accessibility: {
        level: 'full',
        features: defaultAccessibilityFeatures
      },
      bedrooms: 1,
      bathrooms: 1,
      isFavorite: false
    }
  ];

  const handleSearch = (filters: any) => {
    console.log('Búsqueda realizada:', filters);
    // En la implementación real, esto navegaría a la página de resultados
  };

  const handleToggleFavorite = (propertyId: string) => {
    console.log('Toggle favorite:', propertyId);
    // En la implementación real, esto actualizaría el estado de favoritos
  };

  const features = [
    {
      icon: <Accessibility className="w-8 h-8 text-accent-green" />,
      title: 'Viviendas Completamente Adaptadas',
      description: 'Todas nuestras propiedades cumplen con estándares de accesibilidad y están adaptadas para diferentes necesidades.'
    },
    {
      icon: <Shield className="w-8 h-8 text-primary" />,
      title: 'Verificación de Propiedades',
      description: 'Cada propiedad es inspeccionada y verificada por expertos en accesibilidad antes de ser publicada.'
    },
    {
      icon: <Users className="w-8 h-8 text-accent-green" />,
      title: 'Soporte Especializado',
      description: 'Nuestro equipo especializado te acompaña en todo el proceso de búsqueda y selección.'
    },
    {
      icon: <Heart className="w-8 h-8 text-primary" />,
      title: 'Comunidad de Apoyo',
      description: 'Conecta con otros residentes y familias que comparten experiencias similares.'
    }
  ];

  const testimonials = [
    {
      name: 'María González',
      age: 68,
      text: 'Encontrar un apartamento adaptado nunca había sido tan fácil. El equipo me ayudó en cada paso.',
      rating: 5
    },
    {
      name: 'Carlos Ruiz',
      age: 45,
      text: 'Después del accidente, necesitaba una vivienda completamente accesible. CasaAccesible me ayudó a encontrar el hogar perfecto.',
      rating: 5
    },
    {
      name: 'Ana Martín',
      age: 72,
      text: 'La plataforma es muy fácil de usar y las propiedades realmente cumplen con lo que prometen.',
      rating: 5
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section 
        className="relative py-20 px-4 hero-gradient text-white overflow-hidden"
        style={{
          backgroundImage: `linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url(${heroImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
      >
        <div className="container mx-auto text-center relative z-10">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
            Encuentra tu hogar
            <span className="block text-accent-green">accesible y seguro</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
            La primera plataforma especializada en viviendas adaptadas para personas mayores 
            y con discapacidad. Tu independencia es nuestra prioridad.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Button 
              size="lg" 
              className="bg-white text-primary hover:bg-gray-100 px-8 py-3 text-lg font-semibold min-w-[200px]"
              asChild
            >
              <Link to="/propiedades">
                <Search className="mr-2" size={20} />
                Explorar Propiedades
              </Link>
            </Button>
            <Button 
              size="lg" 
              variant="outline" 
              className="border-white text-white hover:bg-white hover:text-primary px-8 py-3 text-lg font-semibold min-w-[200px]"
              asChild
            >
              <Link to="/auth">
                Registrarse Gratis
              </Link>
            </Button>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">500+</div>
              <div className="text-sm opacity-90">Propiedades Verificadas</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">1000+</div>
              <div className="text-sm opacity-90">Familias Satisfechas</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">50+</div>
              <div className="text-sm opacity-90">Ciudades Disponibles</div>
            </div>
          </div>
        </div>
      </section>

      {/* Search Section */}
      <section className="py-16 px-4 bg-muted/50">
        <div className="container mx-auto">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-4">Encuentra tu vivienda ideal</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Utiliza nuestros filtros especializados para encontrar propiedades que se adapten 
              perfectamente a tus necesidades de accesibilidad.
            </p>
          </div>
          
          <SearchBar onSearch={handleSearch} className="max-w-6xl mx-auto" />
        </div>
      </section>

      {/* Premium CTA Section */}
      <section className="py-16 px-4 bg-gradient-to-r from-primary to-primary-foreground text-white">
        <div className="container mx-auto text-center">
          <div className="max-w-4xl mx-auto">
            <div className="flex justify-center mb-4">
              <Crown className="h-12 w-12" />
            </div>
            <h2 className="text-3xl font-bold mb-4">Desbloquea Todo el Potencial</h2>
            <p className="text-xl mb-6 opacity-90">
              Accede a todas las propiedades, filtros avanzados y soporte prioritario por solo 29€/mes
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button 
                size="lg" 
                className="bg-white text-primary hover:bg-gray-100 px-8 py-3 text-lg font-semibold"
                asChild
              >
                <Link to="/suscripcion">
                  <Crown className="mr-2" size={20} />
                  Hazte Premium
                </Link>
              </Button>
              <Button 
                size="lg" 
                variant="outline" 
                className="border-white text-white hover:bg-white hover:text-primary px-6 py-3"
                asChild
              >
                <Link to="/suscripcion">
                  Ver Beneficios
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">¿Por qué elegir CasaAccesible?</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Somos especialistas en viviendas accesibles con años de experiencia 
              ayudando a personas y familias a encontrar su hogar perfecto.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center p-6 hover:shadow-lg transition-shadow">
                <CardContent className="pt-6">
                  <div className="flex justify-center mb-4">
                    {feature.icon}
                  </div>
                  <h3 className="text-lg font-semibold mb-3">{feature.title}</h3>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Properties */}
      <section className="py-16 px-4 bg-muted/50">
        <div className="container mx-auto">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h2 className="text-3xl font-bold mb-2">Propiedades destacadas</h2>
              <p className="text-muted-foreground">
                Descubre algunas de nuestras propiedades más populares
              </p>
            </div>
            <Button variant="outline" asChild>
              <Link to="/propiedades" className="flex items-center gap-2">
                Ver todas
                <ArrowRight size={16} />
              </Link>
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredProperties.map((property) => (
              <PropertyCard
                key={property.id}
                property={property}
                onToggleFavorite={handleToggleFavorite}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Lo que dicen nuestros usuarios</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Historias reales de personas que encontraron su hogar ideal con nuestra ayuda.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="p-6">
                <CardContent className="pt-6">
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} size={16} className="text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <p className="text-sm text-muted-foreground mb-4 italic">
                    "{testimonial.text}"
                  </p>
                  <div className="flex items-center">
                    <div>
                      <p className="font-semibold">{testimonial.name}</p>
                      <p className="text-xs text-muted-foreground">{testimonial.age} años</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 bg-primary text-white">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl font-bold mb-4">¿Listo para encontrar tu hogar?</h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Únete a miles de personas que ya han encontrado su vivienda accesible ideal.
            Es gratis y solo toma unos minutos.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              className="bg-white text-primary hover:bg-gray-100 px-8 py-3 text-lg font-semibold"
              asChild
            >
              <Link to="/auth">
                <CheckCircle className="mr-2" size={20} />
                Registrarse Ahora
              </Link>
            </Button>
            <Button 
              size="lg" 
              variant="outline" 
              className="border-white text-white hover:bg-white hover:text-primary px-8 py-3 text-lg font-semibold"
              asChild
            >
              <Link to="/propiedades">
                Ver Propiedades
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-foreground text-background py-12 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-accent-green rounded-lg flex items-center justify-center">
                  <Accessibility className="w-5 h-5 text-white" />
                </div>
                <span className="text-lg font-bold">CasaAccesible</span>
              </div>
              <p className="text-sm opacity-80">
                La plataforma líder en viviendas adaptadas para personas mayores y con discapacidad.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">Enlaces</h4>
              <ul className="space-y-2 text-sm opacity-80">
                <li><Link to="/propiedades" className="hover:opacity-100">Propiedades</Link></li>
                <li><Link to="/favoritos" className="hover:opacity-100">Favoritos</Link></li>
                <li><Link to="/panel" className="hover:opacity-100">Panel Propietario</Link></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">Soporte</h4>
              <ul className="space-y-2 text-sm opacity-80">
                <li><a href="#" className="hover:opacity-100">Centro de Ayuda</a></li>
                <li><a href="#" className="hover:opacity-100">Contacto</a></li>
                <li><a href="#" className="hover:opacity-100">Accesibilidad</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">Legal</h4>
              <ul className="space-y-2 text-sm opacity-80">
                <li><a href="#" className="hover:opacity-100">Privacidad</a></li>
                <li><a href="#" className="hover:opacity-100">Términos</a></li>
                <li><a href="#" className="hover:opacity-100">Cookies</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-white/20 mt-8 pt-8 text-center text-sm opacity-80">
            <p>&copy; 2024 CasaAccesible. Todos los derechos reservados.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Home;