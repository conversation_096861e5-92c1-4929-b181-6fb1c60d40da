@tailwind base;
@tailwind components;
@tailwind utilities;

/* Ultra-Clean Corporate Design System
Minimalist, professional palette inspired by Apple, Stripe, Notion
Modern sans-serif typography with Inter font family
*/

@layer base {
  :root {
    /* Primary corporate blue - professional and trustworthy */
    --primary: 214 84% 56%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 214 84% 92%;
    --primary-dark: 214 84% 40%;

    /* Ultra-minimal secondary colors */
    --secondary: 210 40% 98%;
    --secondary-foreground: 215 25% 27%;
    --accent: 210 40% 96%;
    --accent-foreground: 215 25% 27%;

    /* Clean backgrounds - pure white and subtle grays */
    --background: 0 0% 100%;
    --foreground: 0 0% 9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 9%;
    --muted: 210 40% 98%;
    --muted-foreground: 215 16% 47%;

    /* Minimal interactive elements */
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 214 84% 56%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 9%;

    /* Status colors - subtle and professional */
    --success: 142 71% 45%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --destructive: 0 72% 51%;
    --destructive-foreground: 0 0% 100%;

    /* Corporate gradients - subtle and elegant */
    --gradient-hero: linear-gradient(135deg, hsl(var(--background)), hsl(var(--muted)));
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-dark)));
    --gradient-card: linear-gradient(180deg, hsl(var(--background)), hsl(var(--card)));
    
    /* Soft shadows for depth */
    --shadow-xs: 0 1px 2px 0 hsl(0 0% 0% / 0.05);
    --shadow-sm: 0 1px 3px 0 hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-md: 0 4px 6px -1px hsl(0 0% 0% / 0.1), 0 2px 4px -2px hsl(0 0% 0% / 0.1);
    --shadow-lg: 0 10px 15px -3px hsl(0 0% 0% / 0.1), 0 4px 6px -4px hsl(0 0% 0% / 0.1);
    --shadow-xl: 0 20px 25px -5px hsl(0 0% 0% / 0.1), 0 8px 10px -6px hsl(0 0% 0% / 0.1);

    /* Modern spacing and radius */
    --radius: 0.5rem;
    --radius-sm: 0.375rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --spacing-unit: 1rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    line-height: 1.6;
    letter-spacing: -0.01em;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Enhanced focus styles for accessibility */
  *:focus-visible {
    outline: 2px solid hsl(var(--focus-ring));
    outline-offset: 2px;
    border-radius: var(--radius);
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    :root {
      --foreground: var(--high-contrast-fg);
      --background: var(--high-contrast-bg);
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Large text mode */
  .large-text {
    font-size: 1.2em;
    line-height: 1.8;
  }

  /* Screen reader only content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
}

@layer components {
  /* Corporate button styles */
  .btn-corporate {
    @apply inline-flex items-center justify-center px-6 py-3 rounded-lg font-medium;
    @apply transition-all duration-200 ease-out;
    @apply focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
    @apply hover:scale-[1.02] hover:shadow-md;
  }

  /* Ultra-clean hero gradient */
  .hero-gradient {
    background: var(--gradient-hero);
  }

  /* Elevated card with subtle shadows */
  .card-elevated {
    box-shadow: var(--shadow-lg);
    @apply transition-all duration-300 ease-out;
  }

  .card-elevated:hover {
    box-shadow: var(--shadow-xl);
    @apply -translate-y-1;
  }

  /* Smooth scroll behavior */
  .smooth-scroll {
    scroll-behavior: smooth;
  }

  /* Professional text styles */
  .text-display {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight;
    font-weight: 700;
    letter-spacing: -0.025em;
  }

  .text-headline {
    @apply text-2xl md:text-3xl lg:text-4xl font-semibold tracking-tight;
    font-weight: 600;
    letter-spacing: -0.02em;
  }

  .text-body {
    @apply text-base leading-relaxed;
    color: hsl(var(--muted-foreground));
  }

  /* Hover animations */
  .hover-lift {
    @apply transition-all duration-200 ease-out hover:-translate-y-1 hover:shadow-lg;
  }

  .hover-scale {
    @apply transition-transform duration-200 ease-out hover:scale-105;
  }

  /* Glass effect */
  .glass {
    @apply backdrop-blur-sm bg-white/80 border border-white/20;
  }

  /* Minimal borders */
  .border-minimal {
    border: 1px solid hsl(var(--border));
  }
}
