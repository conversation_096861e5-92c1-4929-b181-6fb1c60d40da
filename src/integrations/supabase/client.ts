// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://zbpplswzzqkrnmoccsol.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpicHBsc3d6enFrcm5tb2Njc29sIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg2NTMwOTcsImV4cCI6MjA3NDIyOTA5N30.XLcF4eKxD7At4BH8mYBH5LOqnDLGfjK5L4dxLaOt5jY";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});