import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { toast } from 'sonner';

export const useFavorites = () => {
  const [favorites, setFavorites] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  const fetchFavorites = async () => {
    if (!user) {
      setFavorites([]);
      setLoading(false);
      return;
    }

    try {
      const { data, error } = await supabase
        .from('favorites')
        .select('property_id')
        .eq('user_id', user.id);

      if (error) throw error;
      
      setFavorites(data?.map(f => f.property_id) || []);
    } catch (err) {
      console.error('Error fetching favorites:', err);
    } finally {
      setLoading(false);
    }
  };

  const toggleFavorite = async (propertyId: string) => {
    if (!user) {
      toast.error('Debes iniciar sesión para guardar favoritos');
      return;
    }

    try {
      const isFavorite = favorites.includes(propertyId);
      
      if (isFavorite) {
        const { error } = await supabase
          .from('favorites')
          .delete()
          .eq('user_id', user.id)
          .eq('property_id', propertyId);

        if (error) throw error;
        
        setFavorites(prev => prev.filter(id => id !== propertyId));
        toast.success('Eliminado de favoritos');
      } else {
        const { error } = await supabase
          .from('favorites')
          .insert([{
            user_id: user.id,
            property_id: propertyId
          }]);

        if (error) throw error;
        
        setFavorites(prev => [...prev, propertyId]);
        toast.success('Agregado a favoritos');
      }
    } catch (err) {
      console.error('Error toggling favorite:', err);
      toast.error('Error al actualizar favoritos');
    }
  };

  const fetchFavoriteProperties = async () => {
    if (!user) return [];

    try {
      const { data, error } = await supabase
        .from('favorites')
        .select(`
          property_id,
          properties (
            *,
            accessibility_features (*)
          )
        `)
        .eq('user_id', user.id);

      if (error) throw error;
      
      return data?.map(f => f.properties).filter(Boolean) || [];
    } catch (err) {
      console.error('Error fetching favorite properties:', err);
      return [];
    }
  };

  useEffect(() => {
    fetchFavorites();
  }, [user]);

  return {
    favorites,
    loading,
    toggleFavorite,
    fetchFavoriteProperties,
    isFavorite: (propertyId: string) => favorites.includes(propertyId)
  };
};