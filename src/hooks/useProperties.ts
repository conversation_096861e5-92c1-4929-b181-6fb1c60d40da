import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface Property {
  id: string;
  title: string;
  description: string;
  price: number;
  location: string;
  city: string;
  province: string;
  images: string[];
  property_type: 'apartment' | 'house' | 'assisted-living' | 'senior-community';
  accessibility_level: 'basic' | 'intermediate' | 'advanced' | 'full';
  bedrooms: number;
  bathrooms: number;
  size_sqm: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  owner_id: string;
  accessibility_features?: AccessibilityFeature[];
  isFavorite?: boolean;
}

export interface AccessibilityFeature {
  id: string;
  name: string;
  description: string;
  category: 'mobility' | 'visual' | 'hearing' | 'cognitive' | 'safety';
  available: boolean;
  icon: string;
}

export const useProperties = () => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProperties = async (filters?: {
    location?: string;
    propertyType?: string;
    accessibilityLevel?: string;
    minPrice?: number;
    maxPrice?: number;
  }) => {
    try {
      setLoading(true);
      let query = supabase
        .from('properties')
        .select(`
          *,
          accessibility_features (*)
        `)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (filters?.location) {
        query = query.ilike('location', `%${filters.location}%`);
      }
      if (filters?.propertyType) {
        query = query.eq('property_type', filters.propertyType);
      }
      if (filters?.accessibilityLevel) {
        query = query.eq('accessibility_level', filters.accessibilityLevel);
      }
      if (filters?.minPrice) {
        query = query.gte('price', filters.minPrice);
      }
      if (filters?.maxPrice) {
        query = query.lte('price', filters.maxPrice);
      }

      const { data, error } = await query;

      if (error) throw error;
      
      setProperties((data as Property[]) || []);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error fetching properties');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProperties();
  }, []);

  return {
    properties,
    loading,
    error,
    fetchProperties,
    refetch: fetchProperties
  };
};

export const useProperty = (id: string) => {
  const [property, setProperty] = useState<Property | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProperty = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('properties')
          .select(`
            *,
            accessibility_features (*)
          `)
          .eq('id', id)
          .eq('is_active', true)
          .single();

        if (error) throw error;
        
        setProperty(data as Property);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Error fetching property');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchProperty();
    }
  }, [id]);

  return { property, loading, error };
};