import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import type { Property } from '@/hooks/useProperties';

export interface PropertyInput {
  title: string;
  description: string;
  price: number;
  location: string;
  city?: string;
  province?: string;
  property_type: 'apartment' | 'house' | 'assisted-living' | 'senior-community';
  accessibility_level: 'basic' | 'intermediate' | 'advanced' | 'full';
  bedrooms: number;
  bathrooms: number;
  size_sqm?: number;
  images?: string[];
}

export interface OwnerStats {
  totalProperties: number;
  activeListings: number;
  totalViews: number;
  inquiries: number;
  averageRating: number;
}

export const usePropertyManagement = () => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [stats, setStats] = useState<OwnerStats>({
    totalProperties: 0,
    activeListings: 0,
    totalViews: 0,
    inquiries: 0,
    averageRating: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const { user } = useAuth();

  const fetchOwnerProperties = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('properties')
        .select(`
          *,
          accessibility_features (*)
        `)
        .eq('owner_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      setProperties((data as Property[]) || []);
      
      // Calculate stats
      const activeCount = data?.filter(p => p.is_active).length || 0;
      setStats({
        totalProperties: data?.length || 0,
        activeListings: activeCount,
        totalViews: Math.floor(Math.random() * 500) + 100, // Mock data for now
        inquiries: Math.floor(Math.random() * 20) + 5, // Mock data for now
        averageRating: 4.8 // Mock data for now
      });
      
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error fetching properties');
    } finally {
      setLoading(false);
    }
  };

  const createProperty = async (propertyInput: PropertyInput) => {
    if (!user) throw new Error('User not authenticated');
    
    try {
      const { data, error } = await supabase
        .from('properties')
        .insert([
          {
            ...propertyInput,
            owner_id: user.id,
            is_active: true
          }
        ])
        .select()
        .single();

      if (error) throw error;
      
      // Refresh properties list
      await fetchOwnerProperties();
      
      return data;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Error creating property');
    }
  };

  const updateProperty = async (propertyId: string, updates: Partial<PropertyInput>) => {
    if (!user) throw new Error('User not authenticated');
    
    try {
      const { data, error } = await supabase
        .from('properties')
        .update(updates)
        .eq('id', propertyId)
        .eq('owner_id', user.id)
        .select()
        .single();

      if (error) throw error;
      
      // Refresh properties list
      await fetchOwnerProperties();
      
      return data;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Error updating property');
    }
  };

  const deleteProperty = async (propertyId: string) => {
    if (!user) throw new Error('User not authenticated');
    
    try {
      const { error } = await supabase
        .from('properties')
        .delete()
        .eq('id', propertyId)
        .eq('owner_id', user.id);

      if (error) throw error;
      
      // Refresh properties list
      await fetchOwnerProperties();
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Error deleting property');
    }
  };

  const togglePropertyStatus = async (propertyId: string) => {
    if (!user) throw new Error('User not authenticated');
    
    try {
      const property = properties.find(p => p.id === propertyId);
      if (!property) throw new Error('Property not found');
      
      const { error } = await supabase
        .from('properties')
        .update({ is_active: !property.is_active })
        .eq('id', propertyId)
        .eq('owner_id', user.id);

      if (error) throw error;
      
      // Refresh properties list
      await fetchOwnerProperties();
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Error updating property status');
    }
  };

  useEffect(() => {
    if (user) {
      fetchOwnerProperties();
    }
  }, [user]);

  return {
    properties,
    stats,
    loading,
    error,
    createProperty,
    updateProperty,
    deleteProperty,
    togglePropertyStatus,
    refetch: fetchOwnerProperties
  };
};