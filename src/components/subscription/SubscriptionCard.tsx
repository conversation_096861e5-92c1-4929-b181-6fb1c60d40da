import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Crown, CreditCard } from "lucide-react";
import { useSubscription } from "@/hooks/useSubscription";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";

export const SubscriptionCard = () => {
  const { user } = useAuth();
  const { subscriptionData, loading, createCheckoutSession, openCustomerPortal, isPremium } = useSubscription();
  const { toast } = useToast();

  const handleSubscribe = async () => {
    if (!user) {
      toast({
        title: "Iniciar Sesión Requerido",
        description: "Debes iniciar sesión para suscribirte.",
        variant: "destructive",
      });
      return;
    }

    try {
      await createCheckoutSession();
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo crear la sesión de pago. Intenta nuevamente.",
        variant: "destructive",
      });
    }
  };

  const handleManageSubscription = async () => {
    try {
      await openCustomerPortal();
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo abrir el portal de gestión. Intenta nuevamente.",
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Card className="max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="flex items-center justify-center mb-2">
          <Crown className="h-8 w-8 text-primary mr-2" />
          <CardTitle className="text-2xl">Suscripción Premium</CardTitle>
        </div>
        {isPremium && (
          <Badge className="w-fit mx-auto bg-green-100 text-green-800 border-green-300">
            <CheckCircle className="h-4 w-4 mr-1" />
            Activa
          </Badge>
        )}
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="text-center">
          <div className="text-3xl font-bold text-primary">29€</div>
          <div className="text-sm text-muted-foreground">por mes</div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center">
            <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
            <span className="text-sm">Acceso completo a todas las propiedades</span>
          </div>
          <div className="flex items-center">
            <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
            <span className="text-sm">Filtros avanzados de accesibilidad</span>
          </div>
          <div className="flex items-center">
            <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
            <span className="text-sm">Soporte prioritario</span>
          </div>
          <div className="flex items-center">
            <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
            <span className="text-sm">Notificaciones de nuevas propiedades</span>
          </div>
        </div>

        {isPremium && subscriptionData.subscription_end && (
          <div className="text-center text-sm text-muted-foreground">
            Renovación automática el {formatDate(subscriptionData.subscription_end)}
          </div>
        )}

        <div className="space-y-2">
          {!isPremium ? (
            <Button 
              onClick={handleSubscribe} 
              className="w-full"
              disabled={loading}
            >
              <CreditCard className="h-4 w-4 mr-2" />
              {loading ? 'Procesando...' : 'Suscribirse Ahora'}
            </Button>
          ) : (
            <Button 
              onClick={handleManageSubscription} 
              variant="outline" 
              className="w-full"
              disabled={loading}
            >
              <CreditCard className="h-4 w-4 mr-2" />
              Gestionar Suscripción
            </Button>
          )}
        </div>

        {!user && (
          <div className="text-center text-sm text-muted-foreground">
            Necesitas una cuenta para suscribirte
          </div>
        )}
      </CardContent>
    </Card>
  );
};