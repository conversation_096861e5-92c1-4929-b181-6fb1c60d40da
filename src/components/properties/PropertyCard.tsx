import React from 'react';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Heart, MapPin, Users, Accessibility, Eye } from 'lucide-react';
import { Link } from 'react-router-dom';

interface AccessibilityFeature {
  id: string;
  name: string;
  available: boolean;
}

export interface Property {
  id: string;
  title: string;
  description: string;
  price: number;
  location: string;
  images: string[];
  propertyType: 'apartment' | 'house' | 'assisted-living' | 'senior-community';
  accessibility: {
    level: 'basic' | 'intermediate' | 'advanced' | 'full';
    features: AccessibilityFeature[];
  };
  bedrooms: number;
  bathrooms: number;
  isFavorite?: boolean;
}

interface PropertyCardProps {
  property: Property;
  onToggleFavorite: (propertyId: string) => void;
  className?: string;
}

const PropertyCard: React.FC<PropertyCardProps> = ({ 
  property, 
  onToggleFavorite, 
  className = '' 
}) => {
  const getAccessibilityColor = (level: string) => {
    const colors = {
      basic: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      intermediate: 'bg-blue-100 text-blue-800 border-blue-200',
      advanced: 'bg-green-100 text-green-800 border-green-200',
      full: 'bg-accent-green/10 text-accent-green border-accent-green/20'
    };
    return colors[level as keyof typeof colors] || colors.basic;
  };

  const getAccessibilityLabel = (level: string) => {
    const labels = {
      basic: 'Accesibilidad Básica',
      intermediate: 'Accesibilidad Intermedia',
      advanced: 'Accesibilidad Avanzada',
      full: 'Totalmente Adaptada'
    };
    return labels[level as keyof typeof labels] || 'Accesibilidad Básica';
  };

  const getPropertyTypeLabel = (type: string) => {
    const labels = {
      apartment: 'Apartamento',
      house: 'Casa',
      'assisted-living': 'Residencia Asistida',
      'senior-community': 'Comunidad para Seniors'
    };
    return labels[type as keyof typeof labels] || 'Propiedad';
  };

  const accessibilityFeatures = property.accessibility.features.filter(f => f.available).slice(0, 3);

  return (
    <Card className={`group hover:shadow-lg transition-all duration-300 card-elevated ${className}`}>
      {/* Property Image */}
      <div className="relative overflow-hidden rounded-t-lg">
        <img
          src={property.images[0] || '/placeholder.svg'}
          alt={`Vista principal de ${property.title}`}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        
        {/* Favorite Button */}
        <Button
          variant="outline"
          size="sm"
          className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm hover:bg-white"
          onClick={(e) => {
            e.preventDefault();
            onToggleFavorite(property.id);
          }}
          aria-label={property.isFavorite ? 'Quitar de favoritos' : 'Agregar a favoritos'}
        >
          <Heart 
            size={16} 
            className={property.isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-600'} 
          />
        </Button>

        {/* Accessibility Level Badge */}
        <div className="absolute top-3 left-3">
          <Badge 
            className={`${getAccessibilityColor(property.accessibility.level)} border`}
            aria-label={`Nivel de accesibilidad: ${getAccessibilityLabel(property.accessibility.level)}`}
          >
            <Accessibility size={12} className="mr-1" />
            {getAccessibilityLabel(property.accessibility.level)}
          </Badge>
        </div>
      </div>

      <CardContent className="p-4">
        {/* Property Type and Location */}
        <div className="flex items-center justify-between mb-2">
          <Badge variant="secondary" className="text-xs">
            {getPropertyTypeLabel(property.propertyType)}
          </Badge>
          <div className="flex items-center text-sm text-muted-foreground">
            <MapPin size={14} className="mr-1" />
            {property.location}
          </div>
        </div>

        {/* Title and Price */}
        <h3 className="font-semibold text-lg mb-2 line-clamp-2 group-hover:text-primary transition-colors">
          {property.title}
        </h3>
        
        <div className="flex items-center justify-between mb-3">
          <p className="text-2xl font-bold text-primary">
            €{property.price.toLocaleString()}
            <span className="text-sm font-normal text-muted-foreground">/mes</span>
          </p>
          <div className="flex items-center gap-3 text-sm text-muted-foreground">
            <span className="flex items-center">
              <Users size={14} className="mr-1" />
              {property.bedrooms} hab
            </span>
            <span>{property.bathrooms} baños</span>
          </div>
        </div>

        {/* Description */}
        <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
          {property.description}
        </p>

        {/* Accessibility Features */}
        {accessibilityFeatures.length > 0 && (
          <div className="space-y-2">
            <p className="text-xs font-medium text-accent-green">Características de Accesibilidad:</p>
            <div className="flex flex-wrap gap-1">
              {accessibilityFeatures.map((feature) => (
                <Badge 
                  key={feature.id}
                  variant="outline" 
                  className="text-xs accessibility-badge"
                >
                  {feature.name}
                </Badge>
              ))}
              {property.accessibility.features.filter(f => f.available).length > 3 && (
                <Badge variant="outline" className="text-xs text-muted-foreground">
                  +{property.accessibility.features.filter(f => f.available).length - 3} más
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter className="p-4 pt-0">
        <Button 
          className="w-full bg-primary hover:bg-primary-dark" 
          asChild
        >
          <Link 
            to={`/propiedad/${property.id}`}
            className="flex items-center justify-center gap-2"
          >
            <Eye size={16} />
            Ver Detalles
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
};

export default PropertyCard;