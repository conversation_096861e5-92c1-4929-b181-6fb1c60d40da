import React from 'react';
import { Badge } from '@/components/ui/badge';
import { 
  Accessibility, 
  Car, 
  Bath, 
  DoorOpen, 
  Lightbulb, 
  Volume2, 
  Eye, 
  Navigation,
  Zap,
  Phone
} from 'lucide-react';

export interface AccessibilityFeature {
  id: string;
  name: string;
  description: string;
  category: 'mobility' | 'visual' | 'hearing' | 'cognitive' | 'safety';
  available: boolean;
  icon: string;
}

interface AccessibilityBadgesProps {
  features: AccessibilityFeature[];
  showAll?: boolean;
  className?: string;
}

const AccessibilityBadges: React.FC<AccessibilityBadgesProps> = ({ 
  features, 
  showAll = false, 
  className = '' 
}) => {
  const getFeatureIcon = (iconName: string) => {
    const icons: { [key: string]: React.ReactNode } = {
      wheelchair: <Accessibility size={14} />,
      car: <Car size={14} />,
      bath: <Bath size={14} />,
      door: <DoorOpen size={14} />,
      light: <Lightbulb size={14} />,
      sound: <Volume2 size={14} />,
      eye: <Eye size={14} />,
      navigation: <Navigation size={14} />,
      electric: <Zap size={14} />,
      phone: <Phone size={14} />
    };
    return icons[iconName] || <Accessibility size={14} />;
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      mobility: 'bg-blue-100 text-blue-800 border-blue-200',
      visual: 'bg-purple-100 text-purple-800 border-purple-200',
      hearing: 'bg-orange-100 text-orange-800 border-orange-200',
      cognitive: 'bg-green-100 text-green-800 border-green-200',
      safety: 'bg-red-100 text-red-800 border-red-200'
    };
    return colors[category as keyof typeof colors] || colors.mobility;
  };

  const getCategoryLabel = (category: string) => {
    const labels = {
      mobility: 'Movilidad',
      visual: 'Visual',
      hearing: 'Auditiva',
      cognitive: 'Cognitiva',
      safety: 'Seguridad'
    };
    return labels[category as keyof typeof labels] || 'Accesibilidad';
  };

  const availableFeatures = features.filter(f => f.available);
  const displayFeatures = showAll ? availableFeatures : availableFeatures.slice(0, 6);

  if (availableFeatures.length === 0) {
    return (
      <div className={`p-4 bg-muted rounded-lg ${className}`}>
        <p className="text-sm text-muted-foreground text-center">
          No hay características de accesibilidad especificadas
        </p>
      </div>
    );
  }

  const groupedFeatures = displayFeatures.reduce((acc, feature) => {
    if (!acc[feature.category]) {
      acc[feature.category] = [];
    }
    acc[feature.category].push(feature);
    return acc;
  }, {} as Record<string, AccessibilityFeature[]>);

  return (
    <div className={`space-y-4 ${className}`}>
      {Object.entries(groupedFeatures).map(([category, categoryFeatures]) => (
        <div key={category} className="space-y-2">
          <h4 className="text-sm font-semibold text-foreground flex items-center gap-2">
            <span className={`px-2 py-1 rounded text-xs ${getCategoryColor(category)}`}>
              {getCategoryLabel(category)}
            </span>
          </h4>
          
          <div className="flex flex-wrap gap-2">
            {categoryFeatures.map((feature) => (
              <Badge
                key={feature.id}
                variant="outline"
                className="accessibility-badge flex items-center gap-1 px-3 py-1"
                title={feature.description}
              >
                {getFeatureIcon(feature.icon)}
                <span>{feature.name}</span>
              </Badge>
            ))}
          </div>
        </div>
      ))}

      {!showAll && availableFeatures.length > 6 && (
        <div className="text-center">
          <Badge variant="outline" className="text-muted-foreground">
            +{availableFeatures.length - 6} características más
          </Badge>
        </div>
      )}

      {showAll && (
        <div className="mt-4 p-3 bg-accent-green/5 rounded-lg border border-accent-green/20">
          <p className="text-sm text-accent-green font-medium mb-1">
            ¿Necesitas más información sobre accesibilidad?
          </p>
          <p className="text-xs text-muted-foreground">
            Contacta al propietario para obtener detalles específicos sobre las adaptaciones disponibles.
          </p>
        </div>
      )}
    </div>
  );
};

// Default accessibility features for mock data
export const defaultAccessibilityFeatures: AccessibilityFeature[] = [
  {
    id: '1',
    name: 'Entrada con rampa',
    description: 'Rampa de acceso sin escalones desde la calle',
    category: 'mobility',
    available: true,
    icon: 'wheelchair'
  },
  {
    id: '2',
    name: 'Puertas anchas (>80cm)',
    description: 'Todas las puertas tienen más de 80cm de ancho',
    category: 'mobility',
    available: true,
    icon: 'door'
  },
  {
    id: '3',
    name: 'Baño adaptado',
    description: 'Baño con barras de apoyo y ducha sin bordillo',
    category: 'mobility',
    available: true,
    icon: 'bath'
  },
  {
    id: '4',
    name: 'Ascensor',
    description: 'Ascensor espacioso con botones accesibles',
    category: 'mobility',
    available: false,
    icon: 'navigation'
  },
  {
    id: '5',
    name: 'Iluminación LED',
    description: 'Iluminación LED intensa y regulable',
    category: 'visual',
    available: true,
    icon: 'light'
  },
  {
    id: '6',
    name: 'Timbre visual',
    description: 'Sistema de timbre con señales visuales',
    category: 'hearing',
    available: false,
    icon: 'sound'
  },
  {
    id: '7',
    name: 'Parking adaptado',
    description: 'Plaza de parking amplia y señalizada',
    category: 'mobility',
    available: true,
    icon: 'car'
  },
  {
    id: '8',
    name: 'Interruptor a baja altura',
    description: 'Interruptores y enchufes a altura accesible',
    category: 'mobility',
    available: true,
    icon: 'electric'
  }
];

export default AccessibilityBadges;