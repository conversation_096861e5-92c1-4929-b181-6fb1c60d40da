import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, MapPin, Home, Accessibility } from 'lucide-react';
import { Label } from '@/components/ui/label';

interface SearchFilters {
  location: string;
  propertyType: string;
  accessibilityLevel: string;
  priceRange: string;
}

interface SearchBarProps {
  onSearch: (filters: {
    location?: string;
    propertyType?: string;
    accessibilityLevel?: string;
    minPrice?: number;
    maxPrice?: number;
  }) => void;
  className?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({ onSearch, className = '' }) => {
  const [filters, setFilters] = useState<SearchFilters>({
    location: '',
    propertyType: '',
    accessibilityLevel: '',
    priceRange: ''
  });

  const handleSearch = () => {
    // Convert price range to min/max
    let minPrice: number | undefined;
    let maxPrice: number | undefined;
    
    if (filters.priceRange) {
      if (filters.priceRange === '0-500') {
        minPrice = 0;
        maxPrice = 500;
      } else if (filters.priceRange === '500-1000') {
        minPrice = 500;
        maxPrice = 1000;
      } else if (filters.priceRange === '1000-1500') {
        minPrice = 1000;
        maxPrice = 1500;
      } else if (filters.priceRange === '1500-2000') {
        minPrice = 1500;
        maxPrice = 2000;
      } else if (filters.priceRange === '2000+') {
        minPrice = 2000;
      }
    }

    onSearch({
      location: filters.location || undefined,
      propertyType: filters.propertyType || undefined,
      accessibilityLevel: filters.accessibilityLevel || undefined,
      minPrice,
      maxPrice
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className={`bg-white rounded-xl shadow-lg border p-6 ${className}`}>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        {/* Ubicación */}
        <div>
          <Label htmlFor="location" className="flex items-center gap-2 text-sm font-medium mb-2">
            <MapPin size={16} className="text-primary" />
            Ubicación
          </Label>
          <Input
            id="location"
            placeholder="Ciudad, barrio o dirección"
            value={filters.location}
            onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
            onKeyPress={handleKeyPress}
            className="w-full"
            aria-describedby="location-help"
          />
          <p id="location-help" className="sr-only">
            Ingrese la ciudad, barrio o dirección donde buscar propiedades
          </p>
        </div>

        {/* Tipo de Propiedad */}
        <div>
          <Label htmlFor="property-type" className="flex items-center gap-2 text-sm font-medium mb-2">
            <Home size={16} className="text-primary" />
            Tipo de Vivienda
          </Label>
          <Select 
            value={filters.propertyType} 
            onValueChange={(value) => setFilters(prev => ({ ...prev, propertyType: value }))}
            aria-labelledby="property-type"
          >
            <SelectTrigger>
              <SelectValue placeholder="Seleccionar tipo" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="apartment">Apartamento</SelectItem>
              <SelectItem value="house">Casa</SelectItem>
              <SelectItem value="assisted-living">Residencia Asistida</SelectItem>
              <SelectItem value="senior-community">Comunidad para Seniors</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Nivel de Accesibilidad */}
        <div>
          <Label htmlFor="accessibility" className="flex items-center gap-2 text-sm font-medium mb-2">
            <Accessibility size={16} className="text-accent-green" />
            Accesibilidad
          </Label>
          <Select 
            value={filters.accessibilityLevel} 
            onValueChange={(value) => setFilters(prev => ({ ...prev, accessibilityLevel: value }))}
            aria-labelledby="accessibility"
          >
            <SelectTrigger>
              <SelectValue placeholder="Nivel requerido" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="basic">Básica</SelectItem>
              <SelectItem value="intermediate">Intermedia</SelectItem>
              <SelectItem value="advanced">Avanzada</SelectItem>
              <SelectItem value="full">Completamente Adaptada</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Rango de Precio */}
        <div>
          <Label htmlFor="price-range" className="flex items-center gap-2 text-sm font-medium mb-2">
            <span>💰</span>
            Precio
          </Label>
          <Select 
            value={filters.priceRange} 
            onValueChange={(value) => setFilters(prev => ({ ...prev, priceRange: value }))}
            aria-labelledby="price-range"
          >
            <SelectTrigger>
              <SelectValue placeholder="Rango de precio" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0-500">Hasta €500/mes</SelectItem>
              <SelectItem value="500-1000">€500 - €1.000/mes</SelectItem>
              <SelectItem value="1000-1500">€1.000 - €1.500/mes</SelectItem>
              <SelectItem value="1500-2000">€1.500 - €2.000/mes</SelectItem>
              <SelectItem value="2000+">Más de €2.000/mes</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Search Button */}
      <div className="flex justify-center">
        <Button 
          onClick={handleSearch}
          size="lg"
          className="bg-primary hover:bg-primary-dark px-8 py-3 text-lg font-medium min-w-[200px]"
          aria-label="Buscar propiedades con los filtros seleccionados"
        >
          <Search size={20} className="mr-2" />
          Buscar Propiedades
        </Button>
      </div>
    </div>
  );
};

export default SearchBar;