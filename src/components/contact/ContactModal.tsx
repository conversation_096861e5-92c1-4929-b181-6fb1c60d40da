import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { MessageCircle, Phone, Mail, Send } from 'lucide-react';
import { z } from 'zod';

const contactSchema = z.object({
  name: z.string().trim().min(1, "El nombre es requerido").max(100, "El nombre es muy largo"),
  email: z.string().trim().email("Email inválido").max(255, "El email es muy largo"),
  phone: z.string().trim().min(1, "El teléfono es requerido").max(20, "El teléfono es muy largo"),
  message: z.string().trim().min(1, "El mensaje es requerido").max(1000, "El mensaje es muy largo")
});

interface ContactModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  propertyTitle: string;
  propertyId: string;
}

const ContactModal: React.FC<ContactModalProps> = ({ 
  open, 
  onOpenChange, 
  propertyTitle, 
  propertyId 
}) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    message: `Hola, estoy interesado en la propiedad "${propertyTitle}". ¿Podrían darme más información?`
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // Validate form data
      const validatedData = contactSchema.parse(formData);
      
      setIsSubmitting(true);
      
      // Simulate API call - in real implementation, this would send to Supabase
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Mensaje enviado",
        description: "Tu consulta ha sido enviada al propietario. Te contactarán pronto.",
      });
      
      // Reset form and close modal
      setFormData({
        name: '',
        email: '',
        phone: '',
        message: `Hola, estoy interesado en la propiedad "${propertyTitle}". ¿Podrían darme más información?`
      });
      onOpenChange(false);
      
    } catch (error) {
      if (error instanceof z.ZodError) {
        toast({
          title: "Error en el formulario",
          description: error.errors[0].message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Error",
          description: "No se pudo enviar el mensaje. Inténtalo de nuevo.",
          variant: "destructive",
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleWhatsApp = () => {
    const message = encodeURIComponent(
      `Hola, estoy interesado en la propiedad "${propertyTitle}" que vi en CasaAccesible. ¿Podrían darme más información?`
    );
    const whatsappUrl = `https://wa.me/34666666666?text=${message}`;
    window.open(whatsappUrl, '_blank');
    onOpenChange(false);
  };

  const handleCall = () => {
    window.location.href = 'tel:+34666666666';
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Contactar Propietario</DialogTitle>
          <DialogDescription>
            Envía un mensaje sobre "{propertyTitle}"
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Quick Actions */}
          <div className="grid grid-cols-2 gap-3 p-4 bg-muted rounded-lg">
            <Button 
              variant="outline" 
              onClick={handleWhatsApp}
              className="flex items-center gap-2"
            >
              <MessageCircle size={16} className="text-green-600" />
              WhatsApp
            </Button>
            <Button 
              variant="outline" 
              onClick={handleCall}
              className="flex items-center gap-2"
            >
              <Phone size={16} className="text-blue-600" />
              Llamar
            </Button>
          </div>

          {/* Contact Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Nombre *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Tu nombre completo"
                  required
                  maxLength={100}
                />
              </div>
              <div>
                <Label htmlFor="phone">Teléfono *</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="Tu número de teléfono"
                  required
                  maxLength={20}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
                required
                maxLength={255}
              />
            </div>

            <div>
              <Label htmlFor="message">Mensaje *</Label>
              <Textarea
                id="message"
                value={formData.message}
                onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                placeholder="Escribe tu consulta aquí..."
                rows={4}
                required
                maxLength={1000}
              />
              <p className="text-xs text-muted-foreground mt-1">
                {formData.message.length}/1000 caracteres
              </p>
            </div>

            <DialogFooter className="gap-2">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => onOpenChange(false)}
              >
                Cancelar
              </Button>
              <Button 
                type="submit" 
                disabled={isSubmitting}
                className="bg-primary hover:bg-primary-dark"
              >
                {isSubmitting ? (
                  <>Enviando...</>
                ) : (
                  <>
                    <Send size={16} className="mr-2" />
                    Enviar Mensaje
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ContactModal;