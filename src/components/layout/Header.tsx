import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Heart, User, Home, Search, Menu, LogOut, Crown } from 'lucide-react';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { useAuth } from '@/hooks/useAuth';

const Header = () => {
  const location = useLocation();
  const { user, signOut } = useAuth();

  const isActive = (path: string) => location.pathname === path;

  const handleSignOut = async () => {
    await signOut();
  };

  const NavigationLinks = ({ mobile = false }: { mobile?: boolean }) => (
    <nav className={`${mobile ? 'flex flex-col space-y-4' : 'hidden md:flex items-center space-x-6'}`}>
      <Link
        to="/"
        className={`flex items-center gap-2 px-3 py-2 rounded-lg font-medium transition-colors ${
          isActive('/') 
            ? 'bg-primary text-primary-foreground' 
            : 'text-muted-foreground hover:text-foreground hover:bg-muted'
        }`}
        aria-label="Ir a página principal"
      >
        <Home size={18} />
        <span>Inicio</span>
      </Link>
      
      <Link
        to="/propiedades"
        className={`flex items-center gap-2 px-3 py-2 rounded-lg font-medium transition-colors ${
          isActive('/propiedades') 
            ? 'bg-primary text-primary-foreground' 
            : 'text-muted-foreground hover:text-foreground hover:bg-muted'
        }`}
        aria-label="Ver todas las propiedades"
      >
        <Search size={18} />
        <span>Propiedades</span>
      </Link>
      
      <Link
        to="/favoritos"
        className={`flex items-center gap-2 px-3 py-2 rounded-lg font-medium transition-colors ${
          isActive('/favoritos') 
            ? 'bg-primary text-primary-foreground' 
            : 'text-muted-foreground hover:text-foreground hover:bg-muted'
        }`}
        aria-label="Ver propiedades favoritas"
      >
        <Heart size={18} />
        <span>Favoritos</span>
      </Link>

      <Link
        to="/suscripcion"
        className={`flex items-center gap-2 px-3 py-2 rounded-lg font-medium transition-colors ${
          isActive('/suscripcion') 
            ? 'bg-primary text-primary-foreground' 
            : 'text-muted-foreground hover:text-foreground hover:bg-muted'
        }`}
        aria-label="Suscripción Premium"
      >
        <Crown size={18} />
        <span>Premium</span>
      </Link>
    </nav>
  );

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 h-16 flex items-center justify-between">
        {/* Logo */}
        <Link 
          to="/" 
          className="flex items-center space-x-2 text-xl font-bold text-primary hover:opacity-80 transition-opacity"
          aria-label="CasaAccesible - Ir al inicio"
        >
          <div className="w-8 h-8 bg-accent-green rounded-lg flex items-center justify-center">
            <Home className="w-5 h-5 text-white" />
          </div>
          <span className="hidden sm:block">CasaAccesible</span>
        </Link>

        {/* Desktop Navigation */}
        <NavigationLinks />

        {/* Auth Buttons */}
        <div className="flex items-center space-x-3">
          {user ? (
            <>
              <span className="text-sm text-muted-foreground hidden sm:block">
                Hola, {user.email}
              </span>
              
              <Button 
                variant="outline" 
                size="sm"
                asChild
                className="hidden sm:flex"
              >
                <Link to="/dashboard">Panel</Link>
              </Button>
              
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleSignOut}
                className="hidden sm:flex"
              >
                <LogOut size={16} />
              </Button>
            </>
          ) : (
            <>
              <Button 
                variant="outline" 
                size="sm"
                asChild
                className="hidden sm:flex"
              >
                <Link to="/auth">Iniciar Sesión</Link>
              </Button>
              
              <Button 
                size="sm"
                asChild
                className="hidden sm:flex bg-primary hover:bg-primary-dark"
              >
                <Link to="/auth">Registrarse</Link>
              </Button>
            </>
          )}

          {/* Mobile Menu */}
          <Sheet>
            <SheetTrigger asChild>
              <Button 
                variant="outline" 
                size="sm" 
                className="md:hidden"
                aria-label="Abrir menú de navegación"
              >
                <Menu size={16} />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-72">
              <div className="flex flex-col space-y-6 mt-6">
                <NavigationLinks mobile />
                
                <div className="border-t pt-6 space-y-3">
                  {user ? (
                    <>
                      <p className="text-sm text-muted-foreground">
                        Hola, {user.email}
                      </p>
                      <Button variant="outline" className="w-full" asChild>
                        <Link to="/dashboard">Panel de Usuario</Link>
                      </Button>
                      <Button variant="outline" className="w-full" onClick={handleSignOut}>
                        Cerrar Sesión
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button variant="outline" className="w-full" asChild>
                        <Link to="/auth">Iniciar Sesión</Link>
                      </Button>
                      <Button className="w-full bg-primary hover:bg-primary-dark" asChild>
                        <Link to="/auth">Registrarse</Link>
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
};

export default Header;