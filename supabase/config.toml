project_id = "zbpplswzzqkrnmoccsol"

[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[db]
port = 54322

[db.pooler]
enabled = false
port = 54329
pool_mode = "transaction"
default_pool_size = 20
max_client_conn = 100

[realtime]
enabled = true
ip_version = "ipv4"

[studio]
enabled = true
port = 54323
api_url = "http://127.0.0.1:54321"

[inbucket]
enabled = true
port = 54324
smtp_port = 54325
pop3_port = 54326

[storage]
enabled = true
file_size_limit = "50MiB"
image_transformation = { enabled = true }

[auth]
enabled = true
site_url = "http://127.0.0.1:3000"
additional_redirect_urls = ["https://zbpplswzzqkrnmoccsol.supabase.co/auth/v1/callback"]
jwt_expiry = 3600
refresh_token_rotation_enabled = true
security_refresh_token_reuse_interval = 10
enable_signup = true
enable_anonymous_sign_ins = false
minimum_password_length = 6

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = false

[auth.sms]
enable_signup = false
enable_confirmations = false
template = "Your code is {{ .Code }}"

[edge_runtime]
enabled = true
ip_version = "ipv4"

[functions.check-subscription]
verify_jwt = true

[functions.create-checkout]
verify_jwt = true

[functions.customer-portal]
verify_jwt = true