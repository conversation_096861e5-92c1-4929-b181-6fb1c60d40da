-- Fix security vulnerability: Restrict public profile access to non-sensitive fields only
-- Drop the current policy that exposes all profile fields
DROP POLICY IF EXISTS "Public can view basic profile info for property owners" ON public.profiles;

-- Create a secure function that returns only non-sensitive profile data for property owners
CREATE OR REPLACE FUNCTION public.get_public_property_owner_profile(owner_user_id uuid)
RETURNS TABLE (
  user_id uuid,
  display_name text,
  bio text,
  avatar_url text,
  user_type text
)
LANGUAGE sql
SECURITY DEFINER
STABLE
SET search_path = public
AS $$
  SELECT 
    p.user_id,
    p.display_name,
    p.bio,
    p.avatar_url,
    p.user_type
  FROM profiles p
  WHERE p.user_id = owner_user_id
    AND p.user_id IN (
      SELECT DISTINCT owner_id 
      FROM properties 
      WHERE is_active = true
    );
$$;

-- <PERSON> execute permission to public (anon and authenticated users)
GRANT EXECUTE ON FUNCTION public.get_public_property_owner_profile(uuid) TO anon, authenticated;