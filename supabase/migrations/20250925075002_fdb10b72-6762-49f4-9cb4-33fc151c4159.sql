-- Fix security vulnerability: Restrict profiles table access to prevent PII exposure
-- Drop the current overly permissive policy
DROP POLICY IF EXISTS "Profiles are viewable by everyone" ON public.profiles;

-- Create a secure policy that only allows users to view their own profiles
CREATE POLICY "Users can view their own profile" 
ON public.profiles 
FOR SELECT 
USING (auth.uid() = user_id);

-- Create a separate policy for viewing basic public profile information (display_name only)
-- This allows property owners to be identified without exposing sensitive data
CREATE POLICY "Public can view basic profile info for property owners" 
ON public.profiles 
FOR SELECT 
USING (
  user_id IN (
    SELECT DISTINCT owner_id 
    FROM public.properties 
    WHERE is_active = true
  )
);

-- Note: This still protects email, phone, and other sensitive fields through application-level filtering