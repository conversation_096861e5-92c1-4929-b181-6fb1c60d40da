-- Me<PERSON>rar la seguridad de la tabla profiles
-- <PERSON><PERSON>, vamos a asegurar que solo usuarios autenticados puedan acceder a los datos

-- Eliminar las políticas existentes para recrearlas con mayor seguridad
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.profiles;  
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;

-- <PERSON>rear políticas más restrictivas que requieren autenticación
CREATE POLICY "Authenticated users can view only their own profile"
ON public.profiles
FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Authenticated users can insert only their own profile"
ON public.profiles  
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id AND user_id IS NOT NULL);

CREATE POLICY "Authenticated users can update only their own profile"
ON public.profiles
FOR UPDATE
TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id AND user_id IS NOT NULL);

-- Asegurar que user_id no puede ser nulo para evitar brechas de seguridad
ALTER TABLE public.profiles 
ALTER COLUMN user_id SET NOT NULL;

-- Crear una función segura para obtener información pública limitada de propietarios
-- Esta función solo expone campos no sensibles y requiere que el usuario sea propietario activo
CREATE OR REPLACE FUNCTION public.get_safe_owner_info(owner_user_id uuid)
RETURNS TABLE(
  user_id uuid, 
  display_name text, 
  user_type text
)
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path = 'public'
AS $$
  SELECT 
    p.user_id,
    p.display_name,
    p.user_type
  FROM profiles p
  WHERE p.user_id = owner_user_id
    AND p.user_type = 'owner'
    AND p.user_id IN (
      SELECT DISTINCT owner_id 
      FROM properties 
      WHERE is_active = true
    );
$$;